import os
import glob
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.io as sio

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class GCN(torch.nn.Module):
    """
    图卷积神经网络模型
    """
    def __init__(self, num_node_features, hidden_channels, num_classes):
        super(GCN, self).__init__()
        # 第一层图卷积
        self.conv1 = GCNConv(num_node_features, hidden_channels)
        # 第二层图卷积
        self.conv2 = GCNConv(hidden_channels, hidden_channels)
        # 分类层
        self.lin = nn.Linear(hidden_channels, num_classes)

    def forward(self, x, edge_index, batch):
        # 第一层图卷积 + 激活函数
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        
        # 第二层图卷积 + 激活函数
        x = self.conv2(x, edge_index)
        x = F.relu(x)
        
        # 图池化，计算每个图的平均节点特征
        x = global_mean_pool(x, batch)
        
        # 分类层
        x = self.lin(x)
        
        return x

def load_node_features(subject_id, label):
    """
    加载特定被试的节点特征

    参数:
    - subject_id: 被试ID
    - label: 被试标签 (0=HC, 1=MCI)

    返回:
    - node_features: 节点特征矩阵
    """
    # 根据标签确定特征文件路径
    if label == 0:  # HC (健康对照组)
        feature_file = os.path.join('node_features', 'HC', f'ROISignals_{subject_id}.mat')
    elif label == 1:  # MCI (轻度认知障碍)
        feature_file = os.path.join('node_features', 'MCI', f'ROISignals_{subject_id}.mat')
    else:
        raise ValueError(f"未知的标签类型: {label}")
    
    # 检查文件是否存在
    if not os.path.exists(feature_file):
        raise FileNotFoundError(f"节点特征文件不存在: {feature_file}")
    
    # 加载.mat文件
    try:
        mat_data = sio.loadmat(feature_file)
        
        # 提取ROISignals矩阵
        if 'ROISignals' in mat_data:
            signals = mat_data['ROISignals']  # 形状应为 [time_points, n_regions]
            
            # 计算每个节点的特征 (使用时间序列统计特征)
            # 这里使用均值、标准差、最小值、最大值作为特征
            mean = np.mean(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
            std = np.std(signals, axis=0).reshape(-1, 1)    # [n_regions, 1]
            min_val = np.min(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
            max_val = np.max(signals, axis=0).reshape(-1, 1)  # [n_regions, 1]
            
            # 组合特征 [n_regions, 4]
            features = np.hstack([mean, std, min_val, max_val])
            
            return features
        else:
            print(f"警告: 在文件 {feature_file} 中找不到ROISignals数据")
            return None
            
    except Exception as e:
        print(f"读取文件 {feature_file} 时出错: {str(e)}")
        return None

def load_data():
    """
    加载数据并准备用于GCN训练
    
    返回:
    - graph_data: 包含所有图数据的列表
    - labels: 对应的标签
    - subject_ids: 对应的被试ID
    """
    # 读取标签文件
    label_file = 'edge_lists/subject_labels.csv'
    if not os.path.exists(label_file):
        raise FileNotFoundError(f"标签文件不存在: {label_file}")
    
    label_df = pd.read_csv(label_file)
    print(f"标签文件加载完成，包含 {len(label_df)} 个被试")
    print(f"标签文件前5行：")
    print(label_df.head())

    # 获取所有gcn_data文件
    hc_files = sorted(glob.glob('edge_lists/HC/*_gcn_data.npz'))
    mci_files = sorted(glob.glob('edge_lists/MCI/*_gcn_data.npz'))
    all_files = hc_files + mci_files
    
    graph_data = []
    labels = []
    subject_ids = []
    
    # 处理每个文件
    for i, file_path in enumerate(all_files):
        # 提取被试ID
        subject_id = os.path.basename(file_path).split('_gcn_data.npz')[0]

        if i < 5:  # 只打印前5个文件的调试信息
            print(f"处理文件: {file_path}")
            print(f"提取的被试ID: {subject_id}")

        # 查找该被试的标签 (将字符串ID转换为整数以匹配标签文件格式)
        try:
            subject_id_int = int(subject_id)
        except ValueError:
            if i < 5:
                print(f"警告: 无法将被试ID {subject_id} 转换为整数，跳过")
            continue

        subject_label = label_df[label_df['subject_id'] == subject_id_int]['label'].values
        if len(subject_label) == 0:
            if i < 5:  # 只打印前5个的警告
                print(f"警告: 未找到被试 {subject_id_int} 的标签，跳过")
            continue
        label = subject_label[0]
        
        # 加载NPZ文件中的边数据
        npz_data = np.load(file_path)
        edge_index = torch.tensor(npz_data['edge_index'].T, dtype=torch.long)  # PyG期望[2, E]形状
        edge_attr = torch.tensor(npz_data['edge_attr'], dtype=torch.float)
        
        # 获取节点数量
        num_nodes = edge_index.max().item() + 1
        
        # 加载节点特征
        node_features = load_node_features(subject_id, label)
        
        if node_features is not None:
            # 确保节点数量匹配
            if node_features.shape[0] != num_nodes:
                print(f"警告: 被试 {subject_id} 的节点数不匹配，特征数: {node_features.shape[0]}，图中节点数: {num_nodes}")
                # 处理不匹配的情况 (通常应该匹配，除非有特殊情况)
                if node_features.shape[0] > num_nodes:
                    # 截断多余的节点特征
                    node_features = node_features[:num_nodes, :]
                else:
                    # 不足的节点特征填充为0
                    padding = np.zeros((num_nodes - node_features.shape[0], node_features.shape[1]))
                    node_features = np.vstack([node_features, padding])
                    
            # 转换为PyTorch张量
            x = torch.tensor(node_features, dtype=torch.float)
        else:
            # 如果无法加载节点特征，使用默认特征（节点度）
            print(f"警告: 使用节点度作为被试 {subject_id} 的默认节点特征")
            # 计算每个节点的度
            degrees = torch.zeros(num_nodes, dtype=torch.float)
            for node in edge_index[0]:
                degrees[node] += 1
            x = degrees.view(-1, 1)
        
        # 创建PyTorch Geometric的Data对象
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=torch.tensor([label], dtype=torch.long))
        
        # 添加到数据列表
        graph_data.append(data)
        labels.append(label)
        subject_ids.append(subject_id)
    
    return graph_data, np.array(labels), subject_ids

def train_model(model, train_loader, device):
    """
    训练模型
    """
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=5e-4)
    criterion = torch.nn.CrossEntropyLoss()
    
    total_loss = 0
    for data in train_loader:
        data = data.to(device)
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * data.num_graphs
    
    return total_loss / len(train_loader.dataset)

def evaluate_model(model, loader, device):
    """
    评估模型
    """
    model.eval()
    correct = 0
    predictions = []
    targets = []
    
    with torch.no_grad():
        for data in loader:
            data = data.to(device)
            out = model(data.x, data.edge_index, data.batch)
            pred = out.argmax(dim=1)
            correct += int((pred == data.y).sum())
            predictions.extend(pred.cpu().numpy())
            targets.extend(data.y.cpu().numpy())
    
    accuracy = correct / len(loader.dataset)
    f1 = f1_score(targets, predictions, average='macro')
    
    return accuracy, f1, predictions, targets

def cross_validation(graph_data, labels, subject_ids, n_splits=5):
    """
    使用交叉验证评估模型
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 获取节点特征维度
    num_node_features = graph_data[0].x.shape[1]
    print(f"节点特征维度: {num_node_features}")
    
    # 初始化分层K折交叉验证
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
    
    all_accuracies = []
    all_f1_scores = []
    all_predictions = []
    all_targets = []
    all_subject_results = []
    
    # 交叉验证
    for fold, (train_idx, test_idx) in enumerate(skf.split(np.zeros(len(labels)), labels)):
        print(f"\n第 {fold+1}/{n_splits} 折交叉验证")
        
        # 分割训练集和测试集
        train_data = [graph_data[i] for i in train_idx]
        test_data = [graph_data[i] for i in test_idx]
        test_subjects = [subject_ids[i] for i in test_idx]
        
        # 创建数据加载器
        train_loader = DataLoader(train_data, batch_size=32, shuffle=True)
        test_loader = DataLoader(test_data, batch_size=32, shuffle=False)
        
        # 初始化模型
        model = GCN(num_node_features=num_node_features, hidden_channels=64, num_classes=2).to(device)
        
        # 训练模型
        best_val_acc = 0
        patience = 20
        patience_counter = 0
        epochs = 200
        
        for epoch in range(1, epochs + 1):
            loss = train_model(model, train_loader, device)
            train_acc, _, _, _ = evaluate_model(model, train_loader, device)
            val_acc, val_f1, _, _ = evaluate_model(model, test_loader, device)
            
            if epoch % 10 == 0:
                print(f'Epoch: {epoch:03d}, Loss: {loss:.4f}, Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}, Val F1: {val_f1:.4f}')
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), f'best_model_fold{fold+1}.pt')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f'Early stopping at epoch {epoch}')
                    break
        
        # 加载最佳模型
        model.load_state_dict(torch.load(f'best_model_fold{fold+1}.pt'))
        
        # 评估模型
        test_acc, test_f1, preds, targets = evaluate_model(model, test_loader, device)
        print(f'测试集准确率: {test_acc:.4f}, F1分数: {test_f1:.4f}')
        
        all_accuracies.append(test_acc)
        all_f1_scores.append(test_f1)
        all_predictions.extend(preds)
        all_targets.extend(targets)
        
        # 记录每个被试的预测结果
        for i, idx in enumerate(test_idx):
            all_subject_results.append({
                'subject_id': subject_ids[idx],
                'true_label': labels[idx],
                'predicted_label': preds[i],
                'fold': fold + 1
            })
        
        # 混淆矩阵
        cm = confusion_matrix(targets, preds)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                    xticklabels=['HC', 'MCI'], 
                    yticklabels=['HC', 'MCI'])
        plt.xlabel('Predict labels')
        plt.ylabel('True labels')
        plt.title(f'Fold {fold+1} Confusion Matrix')
        plt.savefig(f'confusion_matrix_fold{fold+1}.png')
        plt.close()
    
    # 输出总体结果
    print('\n总体结果:')
    print(f'平均准确率: {np.mean(all_accuracies):.4f} ± {np.std(all_accuracies):.4f}')
    print(f'平均F1分数: {np.mean(all_f1_scores):.4f} ± {np.std(all_f1_scores):.4f}')
    
    # 输出每个被试的结果
    results_df = pd.DataFrame(all_subject_results)
    results_df.to_csv('subject_predictions.csv', index=False)
    print(f'每个被试的预测结果已保存到 subject_predictions.csv')
    
    # 总体混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['HC', 'MCI'], 
                yticklabels=['HC', 'MCI'])
    plt.xlabel('Predict labels')
    plt.ylabel('True labels')
    plt.title('overall confusion matrix')
    plt.savefig('overall_confusion_matrix.png')
    plt.close()

def main():
    print("加载数据...")
    graph_data, labels, subject_ids = load_data()
    print(f"数据加载完成，总共 {len(graph_data)} 个样本")
    
    print(f"开始交叉验证训练...")
    cross_validation(graph_data, labels, subject_ids, n_splits=3)  # 使用3折交叉验证，因为样本较少

if __name__ == "__main__":
    main() 