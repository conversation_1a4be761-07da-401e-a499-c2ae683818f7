import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool, global_max_pool, global_add_pool

class GCNFeatureExtractor(torch.nn.Module):
    """
    GCN特征提取器，用于从图数据中提取特征向量
    """
    def __init__(self, num_node_features, hidden_channels, feature_dim=128, num_layers=3, dropout=0.5):
        super(GCNFeatureExtractor, self).__init__()
        
        self.num_layers = num_layers
        self.dropout = dropout
        self.feature_dim = feature_dim
        
        # 图卷积层
        self.convs = nn.ModuleList()
        self.convs.append(GCNConv(num_node_features, hidden_channels))
        
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_channels, hidden_channels))
        
        self.convs.append(GCNConv(hidden_channels, hidden_channels))
        
        # 批归一化层
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers):
            self.batch_norms.append(nn.BatchNorm1d(hidden_channels))
        
        # 图级别特征提取（存储函数引用，不是模块）
        self.graph_pooling_funcs = [
            global_mean_pool,
            global_max_pool,
            global_add_pool
        ]
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(hidden_channels * 3, hidden_channels * 2),  # 3种池化方式
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels * 2, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 分类头（可选）
        self.classifier = nn.Linear(feature_dim, 2)
        
    def forward(self, x, edge_index, batch, return_features=True):
        """
        前向传播
        
        参数:
        - x: 节点特征 [num_nodes, num_node_features]
        - edge_index: 边索引 [2, num_edges]
        - batch: 批次索引 [num_nodes]
        - return_features: 是否返回特征向量（True）还是分类结果（False）
        
        返回:
        - 如果return_features=True: 特征向量 [batch_size, feature_dim]
        - 如果return_features=False: 分类logits [batch_size, num_classes]
        """
        # 图卷积层
        for i, conv in enumerate(self.convs):
            x = conv(x, edge_index)
            x = self.batch_norms[i](x)
            x = F.relu(x)
            if i < len(self.convs) - 1:  # 最后一层不使用dropout
                x = F.dropout(x, p=self.dropout, training=self.training)
        
        # 图级别池化
        pooled_features = []
        for pool_fn in self.graph_pooling_funcs:
            pooled = pool_fn(x, batch)
            pooled_features.append(pooled)
        
        # 拼接不同池化方式的结果
        graph_features = torch.cat(pooled_features, dim=1)  # [batch_size, hidden_channels * 3]
        
        # 特征融合
        features = self.feature_fusion(graph_features)  # [batch_size, feature_dim]
        
        if return_features:
            return features
        else:
            # 分类
            logits = self.classifier(features)
            return logits
    
    def get_feature_dim(self):
        """返回特征维度"""
        return self.feature_dim


class GCNClassifier(torch.nn.Module):
    """
    完整的GCN分类器，包含特征提取和分类
    """
    def __init__(self, num_node_features, hidden_channels, num_classes=2, feature_dim=128, 
                 num_layers=3, dropout=0.5):
        super(GCNClassifier, self).__init__()
        
        self.feature_extractor = GCNFeatureExtractor(
            num_node_features=num_node_features,
            hidden_channels=hidden_channels,
            feature_dim=feature_dim,
            num_layers=num_layers,
            dropout=dropout
        )
        
        self.classifier = nn.Linear(feature_dim, num_classes)
        
    def forward(self, x, edge_index, batch, return_features=False):
        """
        前向传播
        
        参数:
        - x: 节点特征
        - edge_index: 边索引
        - batch: 批次索引
        - return_features: 是否返回特征向量
        
        返回:
        - 如果return_features=True: (features, logits)
        - 如果return_features=False: logits
        """
        features = self.feature_extractor(x, edge_index, batch, return_features=True)
        logits = self.classifier(features)
        
        if return_features:
            return features, logits
        else:
            return logits
    
    def extract_features(self, x, edge_index, batch):
        """仅提取特征，不进行分类"""
        return self.feature_extractor(x, edge_index, batch, return_features=True)


# 为了兼容性，保留原始的GCN类
class GCN(torch.nn.Module):
    """
    原始的GCN模型（保持向后兼容）
    """
    def __init__(self, num_node_features, hidden_channels, num_classes):
        super(GCN, self).__init__()
        self.conv1 = GCNConv(num_node_features, hidden_channels)
        self.conv2 = GCNConv(hidden_channels, hidden_channels)
        self.lin = nn.Linear(hidden_channels, num_classes)

    def forward(self, x, edge_index, batch):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        
        x = self.conv2(x, edge_index)
        x = F.relu(x)
        
        x = global_mean_pool(x, batch)
        x = self.lin(x)
        
        return x


def create_gcn_feature_extractor(num_node_features, hidden_channels=64, feature_dim=128, 
                                num_layers=3, dropout=0.5):
    """
    创建GCN特征提取器的便捷函数
    
    参数:
    - num_node_features: 节点特征维度
    - hidden_channels: 隐藏层维度
    - feature_dim: 输出特征维度
    - num_layers: GCN层数
    - dropout: dropout率
    
    返回:
    - GCNFeatureExtractor实例
    """
    return GCNFeatureExtractor(
        num_node_features=num_node_features,
        hidden_channels=hidden_channels,
        feature_dim=feature_dim,
        num_layers=num_layers,
        dropout=dropout
    )


def create_gcn_classifier(num_node_features, hidden_channels=64, num_classes=2, 
                         feature_dim=128, num_layers=3, dropout=0.5):
    """
    创建GCN分类器的便捷函数
    
    参数:
    - num_node_features: 节点特征维度
    - hidden_channels: 隐藏层维度
    - num_classes: 类别数
    - feature_dim: 特征维度
    - num_layers: GCN层数
    - dropout: dropout率
    
    返回:
    - GCNClassifier实例
    """
    return GCNClassifier(
        num_node_features=num_node_features,
        hidden_channels=hidden_channels,
        num_classes=num_classes,
        feature_dim=feature_dim,
        num_layers=num_layers,
        dropout=dropout
    )
