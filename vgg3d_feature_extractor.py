import torch
import torch.nn as nn
import torch.nn.functional as F

class VGG3DFeatureExtractor(nn.Module):
    """
    3D VGG特征提取器，用于从3D MRI图像中提取特征向量
    """
    def __init__(self, features, feature_dim=128, init_weights=False):
        super(VGG3DFeatureExtractor, self).__init__()
        self.features = features
        self.feature_dim = feature_dim
        
        # 自适应平均池化层，统一特征图尺寸
        self.adaptive_avg_pool = nn.AdaptiveAvgPool3d((4, 4, 4))
        
        # 特征提取层（不包含最终分类层）
        self.feature_extractor = nn.Sequential(
            nn.Linear(512*4*4*4, 4096),
            nn.ReL<PERSON>(True),
            nn.Dropout(p=0.5),
            nn.Linear(4096, 2048),
            nn.ReLU(True),
            nn.Dropout(p=0.5),
            nn.Linear(2048, feature_dim),
            nn.ReLU(True)
        )
        
        # 分类头（可选）
        self.classifier = nn.Linear(feature_dim, 2)
        
        if init_weights:
            self._initialize_weights()

    def forward(self, x, return_features=True):
        """
        前向传播
        
        参数:
        - x: 输入3D图像 [batch_size, channels, depth, height, width]
        - return_features: 是否返回特征向量（True）还是分类结果（False）
        
        返回:
        - 如果return_features=True: 特征向量 [batch_size, feature_dim]
        - 如果return_features=False: 分类logits [batch_size, num_classes]
        """
        # 特征提取
        x = self.features(x)
        
        # 自适应池化统一尺寸
        x = self.adaptive_avg_pool(x)
        
        # 展平特征图
        x = torch.flatten(x, start_dim=1)
        
        # 特征提取
        features = self.feature_extractor(x)
        
        if return_features:
            return features
        else:
            # 分类
            logits = self.classifier(features)
            return logits

    def get_feature_dim(self):
        """返回特征维度"""
        return self.feature_dim

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)


class VGG3DClassifier(nn.Module):
    """
    完整的3D VGG分类器，包含特征提取和分类
    """
    def __init__(self, features, num_classes=2, feature_dim=128, init_weights=False):
        super(VGG3DClassifier, self).__init__()
        
        self.feature_extractor = VGG3DFeatureExtractor(
            features=features,
            feature_dim=feature_dim,
            init_weights=init_weights
        )
        
        self.classifier = nn.Linear(feature_dim, num_classes)
        
    def forward(self, x, return_features=False):
        """
        前向传播
        
        参数:
        - x: 输入3D图像
        - return_features: 是否返回特征向量
        
        返回:
        - 如果return_features=True: (features, logits)
        - 如果return_features=False: logits
        """
        features = self.feature_extractor(x, return_features=True)
        logits = self.classifier(features)
        
        if return_features:
            return features, logits
        else:
            return logits
    
    def extract_features(self, x):
        """仅提取特征，不进行分类"""
        return self.feature_extractor(x, return_features=True)


# 保留原始VGG类以保持兼容性
class VGG(nn.Module):
    def __init__(self, features, num_classes=1000, init_weights=False):
        super(VGG, self).__init__()
        self.features = features
        self.adaptive_avg_pool = nn.AdaptiveAvgPool3d((4, 4, 4))
        self.classifier = nn.Sequential(
            nn.Linear(512*4*4*4, 4096),
            nn.ReLU(True),
            nn.Dropout(p=0.5),
            nn.Linear(4096, 4096),
            nn.ReLU(True),
            nn.Dropout(p=0.5),
            nn.Linear(4096, num_classes)
        )
        if init_weights:
            self._initialize_weights()

    def forward(self, x):
        x = self.features(x)
        x = self.adaptive_avg_pool(x)
        x = torch.flatten(x, start_dim=1)
        x = self.classifier(x)
        return x

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)


def make_features(cfg: list, in_channels=1):
    """创建VGG特征提取层"""
    layers = []
    for v in cfg:
        if v == "M":
            layers += [nn.MaxPool3d(kernel_size=2, stride=2)]
        else:
            conv3d = nn.Conv3d(in_channels, v, kernel_size=3, padding=1)
            layers += [conv3d, nn.ReLU(True)]
            in_channels = v
    return nn.Sequential(*layers)


# VGG配置
cfgs = {
    'vgg11': [64, 'M', 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg13': [64, 64, 'M', 128, 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg16': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 512, 512, 512, 'M', 512, 512, 512, 'M'],
    'vgg19': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 256, 'M', 512, 512, 512, 512, 'M', 512, 512, 512, 512, 'M'],
}


def create_vgg3d_feature_extractor(model_name="vgg16", in_channels=1, feature_dim=128, init_weights=True):
    """
    创建3D VGG特征提取器的便捷函数
    
    参数:
    - model_name: VGG模型名称
    - in_channels: 输入通道数
    - feature_dim: 输出特征维度
    - init_weights: 是否初始化权重
    
    返回:
    - VGG3DFeatureExtractor实例
    """
    assert model_name in cfgs, f"Warning: model number {model_name} not in cfgs dict!"
    cfg = cfgs[model_name]
    features = make_features(cfg, in_channels=in_channels)
    
    return VGG3DFeatureExtractor(
        features=features,
        feature_dim=feature_dim,
        init_weights=init_weights
    )


def create_vgg3d_classifier(model_name="vgg16", in_channels=1, num_classes=2, 
                           feature_dim=128, init_weights=True):
    """
    创建3D VGG分类器的便捷函数
    
    参数:
    - model_name: VGG模型名称
    - in_channels: 输入通道数
    - num_classes: 类别数
    - feature_dim: 特征维度
    - init_weights: 是否初始化权重
    
    返回:
    - VGG3DClassifier实例
    """
    assert model_name in cfgs, f"Warning: model number {model_name} not in cfgs dict!"
    cfg = cfgs[model_name]
    features = make_features(cfg, in_channels=in_channels)
    
    return VGG3DClassifier(
        features=features,
        num_classes=num_classes,
        feature_dim=feature_dim,
        init_weights=init_weights
    )


# 保留原始vgg函数以保持兼容性
def vgg(model_name="vgg16", in_channels=1, **kwargs):
    assert model_name in cfgs, "Warning: model number {} not in cfgs dict!".format(model_name)
    cfg = cfgs[model_name]
    model = VGG(make_features(cfg, in_channels=in_channels), **kwargs)
    return model
