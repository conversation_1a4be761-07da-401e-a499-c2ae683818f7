import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class BilinearAttentionFusion(nn.Module):
    """
    双线性注意力融合模块
    用于融合来自不同模态的特征向量
    """
    def __init__(self, feature_dim1, feature_dim2, fusion_dim=256, attention_dim=128, dropout=0.3):
        """
        初始化双线性注意力融合模块
        
        参数:
        - feature_dim1: 第一个模态的特征维度（如GCN特征）
        - feature_dim2: 第二个模态的特征维度（如VGG特征）
        - fusion_dim: 融合后的特征维度
        - attention_dim: 注意力机制的隐藏维度
        - dropout: dropout率
        """
        super(BilinearAttentionFusion, self).__init__()
        
        self.feature_dim1 = feature_dim1
        self.feature_dim2 = feature_dim2
        self.fusion_dim = fusion_dim
        self.attention_dim = attention_dim
        
        # 特征投影层，将不同维度的特征投影到相同维度
        self.proj1 = nn.Linear(feature_dim1, fusion_dim)
        self.proj2 = nn.Linear(feature_dim2, fusion_dim)
        
        # 双线性注意力层
        self.bilinear_attention = nn.Bilinear(fusion_dim, fusion_dim, attention_dim)
        
        # 注意力权重计算
        self.attention_weights = nn.Sequential(
            nn.Linear(attention_dim, attention_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(attention_dim // 2, 2),  # 2个模态的权重
            nn.Softmax(dim=1)
        )
        
        # 自注意力机制
        self.self_attention1 = SelfAttention(fusion_dim, attention_dim)
        self.self_attention2 = SelfAttention(fusion_dim, attention_dim)
        
        # 特征融合层
        self.fusion_layers = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim, fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 残差连接的投影层
        self.residual_proj = nn.Linear(fusion_dim * 2, fusion_dim // 2)
        
        # 层归一化
        self.layer_norm1 = nn.LayerNorm(fusion_dim)
        self.layer_norm2 = nn.LayerNorm(fusion_dim)
        self.layer_norm_fusion = nn.LayerNorm(fusion_dim // 2)
        
    def forward(self, features1, features2):
        """
        前向传播
        
        参数:
        - features1: 第一个模态的特征 [batch_size, feature_dim1]
        - features2: 第二个模态的特征 [batch_size, feature_dim2]
        
        返回:
        - fused_features: 融合后的特征 [batch_size, fusion_dim // 2]
        - attention_weights: 注意力权重 [batch_size, 2]
        """
        batch_size = features1.size(0)
        
        # 特征投影
        proj_feat1 = self.proj1(features1)  # [batch_size, fusion_dim]
        proj_feat2 = self.proj2(features2)  # [batch_size, fusion_dim]
        
        # 层归一化
        proj_feat1 = self.layer_norm1(proj_feat1)
        proj_feat2 = self.layer_norm2(proj_feat2)
        
        # 自注意力增强
        enhanced_feat1 = self.self_attention1(proj_feat1.unsqueeze(1)).squeeze(1)
        enhanced_feat2 = self.self_attention2(proj_feat2.unsqueeze(1)).squeeze(1)
        
        # 双线性注意力计算
        bilinear_output = self.bilinear_attention(enhanced_feat1, enhanced_feat2)  # [batch_size, attention_dim]
        
        # 计算注意力权重
        attention_weights = self.attention_weights(bilinear_output)  # [batch_size, 2]
        
        # 应用注意力权重
        weighted_feat1 = enhanced_feat1 * attention_weights[:, 0:1]  # [batch_size, fusion_dim]
        weighted_feat2 = enhanced_feat2 * attention_weights[:, 1:2]  # [batch_size, fusion_dim]
        
        # 特征拼接
        concatenated = torch.cat([weighted_feat1, weighted_feat2], dim=1)  # [batch_size, fusion_dim * 2]
        
        # 特征融合
        fused_features = self.fusion_layers(concatenated)  # [batch_size, fusion_dim // 2]
        
        # 残差连接
        residual = self.residual_proj(concatenated)  # [batch_size, fusion_dim // 2]
        fused_features = fused_features + residual
        
        # 层归一化
        fused_features = self.layer_norm_fusion(fused_features)
        
        return fused_features, attention_weights


class SelfAttention(nn.Module):
    """
    自注意力机制模块
    """
    def __init__(self, feature_dim, attention_dim):
        super(SelfAttention, self).__init__()
        
        self.feature_dim = feature_dim
        self.attention_dim = attention_dim
        
        # 查询、键、值投影层
        self.query = nn.Linear(feature_dim, attention_dim)
        self.key = nn.Linear(feature_dim, attention_dim)
        self.value = nn.Linear(feature_dim, feature_dim)
        
        # 输出投影层
        self.output_proj = nn.Linear(feature_dim, feature_dim)
        
        # 缩放因子
        self.scale = math.sqrt(attention_dim)
        
    def forward(self, x):
        """
        前向传播
        
        参数:
        - x: 输入特征 [batch_size, seq_len, feature_dim]
        
        返回:
        - output: 自注意力输出 [batch_size, seq_len, feature_dim]
        """
        batch_size, seq_len, _ = x.size()
        
        # 计算查询、键、值
        Q = self.query(x)  # [batch_size, seq_len, attention_dim]
        K = self.key(x)    # [batch_size, seq_len, attention_dim]
        V = self.value(x)  # [batch_size, seq_len, feature_dim]
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale  # [batch_size, seq_len, seq_len]
        attention_weights = F.softmax(attention_scores, dim=-1)
        
        # 应用注意力权重
        attended_values = torch.matmul(attention_weights, V)  # [batch_size, seq_len, feature_dim]
        
        # 输出投影
        output = self.output_proj(attended_values)
        
        # 残差连接
        output = output + x
        
        return output


class MultiModalAttentionFusion(nn.Module):
    """
    多模态注意力融合模块（支持更多模态）
    """
    def __init__(self, feature_dims, fusion_dim=256, attention_dim=128, dropout=0.3):
        """
        初始化多模态注意力融合模块
        
        参数:
        - feature_dims: 各模态特征维度列表
        - fusion_dim: 融合后的特征维度
        - attention_dim: 注意力机制的隐藏维度
        - dropout: dropout率
        """
        super(MultiModalAttentionFusion, self).__init__()
        
        self.num_modalities = len(feature_dims)
        self.feature_dims = feature_dims
        self.fusion_dim = fusion_dim
        
        # 特征投影层
        self.projections = nn.ModuleList([
            nn.Linear(dim, fusion_dim) for dim in feature_dims
        ])
        
        # 交叉注意力层
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 注意力权重计算
        self.attention_weights = nn.Sequential(
            nn.Linear(fusion_dim, attention_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(attention_dim, self.num_modalities),
            nn.Softmax(dim=1)
        )
        
        # 特征融合层
        self.fusion_layers = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(fusion_dim // 2, fusion_dim // 4),
            nn.ReLU()
        )
        
    def forward(self, features_list):
        """
        前向传播
        
        参数:
        - features_list: 各模态特征列表 [feature1, feature2, ...]
        
        返回:
        - fused_features: 融合后的特征
        - attention_weights: 注意力权重
        """
        batch_size = features_list[0].size(0)
        
        # 特征投影
        projected_features = []
        for i, features in enumerate(features_list):
            proj_feat = self.projections[i](features)
            projected_features.append(proj_feat)
        
        # 堆叠特征用于交叉注意力
        stacked_features = torch.stack(projected_features, dim=1)  # [batch_size, num_modalities, fusion_dim]
        
        # 交叉注意力
        attended_features, _ = self.cross_attention(
            stacked_features, stacked_features, stacked_features
        )  # [batch_size, num_modalities, fusion_dim]
        
        # 计算全局表示用于注意力权重
        global_repr = torch.mean(attended_features, dim=1)  # [batch_size, fusion_dim]
        
        # 计算注意力权重
        attention_weights = self.attention_weights(global_repr)  # [batch_size, num_modalities]
        
        # 应用注意力权重
        weighted_features = attended_features * attention_weights.unsqueeze(-1)  # [batch_size, num_modalities, fusion_dim]
        
        # 特征聚合
        aggregated_features = torch.sum(weighted_features, dim=1)  # [batch_size, fusion_dim]
        
        # 特征融合
        fused_features = self.fusion_layers(aggregated_features)
        
        return fused_features, attention_weights
