import os
import glob
import numpy as np
import pandas as pd
import torch
import nibabel as nib
import scipy.io as sio
from torch.utils.data import Dataset
from torch_geometric.data import Data
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip, 
    RandZoom, Resize, ToTensor
)

class MultiModalDataset(Dataset):
    """
    多模态数据集，同时加载fMRI功能连接矩阵和sMRI灰质体积图
    """
    def __init__(self, fmri_dir, smri_dir, edge_lists_dir, node_features_dir, 
                 classes=['HC', 'MCI'], transform_smri=None, use_edge_lists=True):
        """
        初始化多模态数据集
        
        参数:
        - fmri_dir: fMRI数据目录
        - smri_dir: sMRI数据目录  
        - edge_lists_dir: 边列表数据目录
        - node_features_dir: 节点特征目录
        - classes: 类别列表
        - transform_smri: sMRI数据变换
        - use_edge_lists: 是否使用预处理的边列表数据
        """
        self.fmri_dir = fmri_dir
        self.smri_dir = smri_dir
        self.edge_lists_dir = edge_lists_dir
        self.node_features_dir = node_features_dir
        self.classes = classes
        self.transform_smri = transform_smri
        self.use_edge_lists = use_edge_lists
        
        # 加载标签文件
        label_file = os.path.join(edge_lists_dir, 'subject_labels.csv')
        if os.path.exists(label_file):
            self.label_df = pd.read_csv(label_file)
        else:
            raise FileNotFoundError(f"标签文件不存在: {label_file}")
        
        # 收集所有有效的被试数据
        self.valid_subjects = self._collect_valid_subjects()
        print(f"找到 {len(self.valid_subjects)} 个有效的多模态被试数据")
        
    def _collect_valid_subjects(self):
        """收集所有同时具有fMRI和sMRI数据的被试"""
        valid_subjects = []
        
        for class_idx, class_name in enumerate(self.classes):
            # 获取fMRI数据文件
            if self.use_edge_lists:
                fmri_files = glob.glob(os.path.join(self.edge_lists_dir, class_name, '*_gcn_data.npz'))
            else:
                fmri_files = glob.glob(os.path.join(self.fmri_dir, class_name, '*_knn_3.mat'))
            
            # 获取sMRI数据文件
            smri_files = glob.glob(os.path.join(self.smri_dir, class_name, '*.nii*'))
            
            # 提取被试ID
            fmri_subject_ids = set()
            for f in fmri_files:
                if self.use_edge_lists:
                    subject_id = os.path.basename(f).split('_gcn_data.npz')[0]
                else:
                    subject_id = os.path.basename(f).split('_knn_3.mat')[0]
                fmri_subject_ids.add(subject_id)
            
            smri_subject_ids = set()
            for f in smri_files:
                # 从sMRI文件名中提取被试ID
                filename = os.path.basename(f)
                # 处理不同的文件名格式
                if filename.startswith('smwp1'):
                    # 提取类似 "01001", "02001" 等格式的ID
                    parts = filename.split('_')[0]  # smwp101001
                    if len(parts) >= 8:  # smwp1 + 5位数字
                        subject_id = parts[5:]  # 提取后5位
                        # 转换为标准格式，如 "01001"
                        if len(subject_id) == 5:
                            smri_subject_ids.add(subject_id)
            
            # 找到同时具有fMRI和sMRI数据的被试
            common_subjects = fmri_subject_ids.intersection(smri_subject_ids)
            
            for subject_id in common_subjects:
                # 检查标签文件中是否有该被试
                try:
                    subject_id_int = int(subject_id)
                    subject_label = self.label_df[self.label_df['subject_id'] == subject_id_int]['label'].values
                    if len(subject_label) > 0:
                        valid_subjects.append({
                            'subject_id': subject_id,
                            'class_name': class_name,
                            'class_idx': class_idx,
                            'label': subject_label[0]
                        })
                except ValueError:
                    continue
                    
        return valid_subjects
    
    def __len__(self):
        return len(self.valid_subjects)
    
    def __getitem__(self, idx):
        """获取一个多模态样本"""
        subject_info = self.valid_subjects[idx]
        subject_id = subject_info['subject_id']
        class_name = subject_info['class_name']
        label = subject_info['label']
        
        # 加载fMRI数据
        fmri_data = self._load_fmri_data(subject_id, class_name, label)
        
        # 加载sMRI数据
        smri_data = self._load_smri_data(subject_id, class_name)
        
        return {
            'fmri': fmri_data,
            'smri': smri_data,
            'label': torch.tensor(label, dtype=torch.long),
            'subject_id': subject_id
        }
    
    def _load_fmri_data(self, subject_id, class_name, label):
        """加载fMRI数据（图数据）"""
        if self.use_edge_lists:
            # 使用预处理的边列表数据
            file_path = os.path.join(self.edge_lists_dir, class_name, f'{subject_id}_gcn_data.npz')
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"fMRI边列表文件不存在: {file_path}")
            
            # 加载NPZ文件
            npz_data = np.load(file_path)
            edge_index = torch.tensor(npz_data['edge_index'].T, dtype=torch.long)
            edge_attr = torch.tensor(npz_data['edge_attr'], dtype=torch.float)
            
            # 获取节点数量
            num_nodes = edge_index.max().item() + 1
            
            # 加载节点特征
            node_features = self._load_node_features(subject_id, label)
            
            if node_features is not None:
                # 确保节点数量匹配
                if node_features.shape[0] != num_nodes:
                    if node_features.shape[0] > num_nodes:
                        node_features = node_features[:num_nodes, :]
                    else:
                        padding = np.zeros((num_nodes - node_features.shape[0], node_features.shape[1]))
                        node_features = np.vstack([node_features, padding])
                x = torch.tensor(node_features, dtype=torch.float)
            else:
                # 使用节点度作为默认特征
                degrees = torch.zeros(num_nodes, dtype=torch.float)
                for node in edge_index[0]:
                    degrees[node] += 1
                x = degrees.view(-1, 1)
            
            # 创建图数据对象
            graph_data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
            return graph_data
        else:
            # 直接使用功能连接矩阵
            file_path = os.path.join(self.fmri_dir, class_name, f'{subject_id}_knn_3.mat')
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"fMRI连接矩阵文件不存在: {file_path}")
            
            mat_data = sio.loadmat(file_path)
            # 假设连接矩阵存储在某个键中，需要根据实际情况调整
            if 'connectivity_matrix' in mat_data:
                conn_matrix = mat_data['connectivity_matrix']
            else:
                # 尝试其他可能的键名
                keys = [k for k in mat_data.keys() if not k.startswith('__')]
                if keys:
                    conn_matrix = mat_data[keys[0]]
                else:
                    raise ValueError(f"无法在文件 {file_path} 中找到连接矩阵")
            
            return torch.tensor(conn_matrix, dtype=torch.float)

    def _load_node_features(self, subject_id, label):
        """加载节点特征"""
        # 根据标签确定特征文件路径
        if label == 0:  # HC
            feature_file = os.path.join(self.node_features_dir, 'HC', f'ROISignals_{subject_id}.mat')
        elif label == 1:  # MCI
            feature_file = os.path.join(self.node_features_dir, 'MCI', f'ROISignals_{subject_id}.mat')
        else:
            return None

        if not os.path.exists(feature_file):
            return None

        try:
            mat_data = sio.loadmat(feature_file)
            if 'ROISignals' in mat_data:
                signals = mat_data['ROISignals']  # [time_points, n_regions]

                # 计算统计特征
                mean = np.mean(signals, axis=0).reshape(-1, 1)
                std = np.std(signals, axis=0).reshape(-1, 1)
                min_val = np.min(signals, axis=0).reshape(-1, 1)
                max_val = np.max(signals, axis=0).reshape(-1, 1)

                # 组合特征 [n_regions, 4]
                features = np.hstack([mean, std, min_val, max_val])
                return features
            else:
                return None
        except Exception:
            return None

    def _load_smri_data(self, subject_id, class_name):
        """加载sMRI数据（3D体积图）"""
        # 查找对应的sMRI文件
        smri_pattern = os.path.join(self.smri_dir, class_name, f'*{subject_id}*.nii*')
        smri_files = glob.glob(smri_pattern)

        if not smri_files:
            raise FileNotFoundError(f"找不到被试 {subject_id} 的sMRI文件")

        # 使用第一个匹配的文件
        smri_file = smri_files[0]

        # 加载NIfTI文件
        img = nib.load(smri_file)
        image_data = img.get_fdata(dtype=np.float32)

        # 直接返回原始数据，变换将在训练循环中应用
        return torch.tensor(image_data, dtype=torch.float)


def add_channel_dim(x):
    """为3D图像添加通道维度"""
    return x[None, ...]


def create_multimodal_transforms():
    """创建多模态数据变换"""
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])

    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])

    return train_transform, val_transform


def multimodal_collate_fn(batch):
    """
    自定义的批处理函数，用于处理多模态数据
    """
    fmri_data = []
    smri_data = []
    labels = []
    subject_ids = []

    for sample in batch:
        fmri_data.append(sample['fmri'])
        smri_data.append(sample['smri'])
        labels.append(sample['label'])
        subject_ids.append(sample['subject_id'])

    # 对于sMRI数据，可以直接堆叠
    smri_batch = torch.stack(smri_data)

    # 对于fMRI图数据，需要使用PyG的批处理方法
    from torch_geometric.data import Batch
    fmri_batch = Batch.from_data_list(fmri_data)

    labels_batch = torch.stack(labels)

    return {
        'fmri': fmri_batch,
        'smri': smri_batch,
        'labels': labels_batch,
        'subject_ids': subject_ids
    }
