# 多模态深度学习系统 - fMRI + sMRI 分类

## 项目概述

本项目实现了一个先进的多模态深度学习系统，结合功能磁共振成像(fMRI)功能连接矩阵和结构磁共振成像(sMRI)灰质体积图像，用于健康对照组(HC)和轻度认知障碍(MCI)的自动分类。

## 系统架构

### 核心组件

1. **GCN特征提取器** (`gcn_feature_extractor.py`)
   - 基于图卷积网络处理fMRI功能连接数据
   - 支持多种图级别池化策略(mean, max, add)
   - 输出固定维度的特征向量

2. **3D VGG特征提取器** (`vgg3d_feature_extractor.py`)
   - 基于3D VGG网络处理sMRI体积数据
   - 支持多种VGG架构配置
   - 自适应池化确保固定维度输出

3. **双线性注意力融合模块** (`bilinear_attention_fusion.py`)
   - 实现sophisticated的注意力机制
   - 支持自注意力和交叉注意力
   - 包含残差连接和层归一化

4. **多模态分类器** (`multimodal_model.py`)
   - 整合所有组件的端到端模型
   - 支持单模态和多模态分类
   - 包含集成学习功能

### 数据处理

5. **多模态数据加载器** (`multimodal_dataset.py`)
   - 自动匹配同一被试的不同模态数据
   - 支持灵活的数据变换和增强
   - 处理不同文件命名约定

## 文件结构

```
├── multimodal_dataset.py          # 多模态数据加载器
├── gcn_feature_extractor.py       # GCN特征提取器
├── vgg3d_feature_extractor.py     # 3D VGG特征提取器
├── bilinear_attention_fusion.py   # 双线性注意力融合
├── multimodal_model.py            # 完整多模态模型
├── train_multimodal.py            # 训练脚本
├── test_multimodal_system.py      # 系统测试脚本
├── demo_multimodal.py             # 演示脚本
└── README_multimodal.md           # 本文档
```

## 数据要求

### 目录结构
```
data_fmri/
├── HC/                 # 健康对照组fMRI数据
└── MCI/                # MCI患者fMRI数据

data_smri/
├── HC/                 # 健康对照组sMRI数据
└── MCI/                # MCI患者sMRI数据

edge_lists/
├── HC/                 # 功能连接边列表
├── MCI/
└── subject_labels.csv  # 被试标签文件

node_features/
├── HC/                 # 节点特征文件
└── MCI/
```

### 数据格式
- **fMRI数据**: 功能连接矩阵，支持.mat或.npy格式
- **sMRI数据**: 3D体积图像，支持.nii或.nii.gz格式
- **边列表**: 图的边连接信息
- **节点特征**: 图节点的特征向量

## 使用方法

### 1. 系统测试
```bash
python test_multimodal_system.py
```
验证所有组件是否正常工作。

### 2. 数据探索
```bash
python demo_multimodal.py --mode explore
```
分析数据集分布和维度信息。

### 3. 模型架构查看
```bash
python demo_multimodal.py --mode architecture
```
查看模型结构和参数统计。

### 4. 特征提取演示
```bash
python demo_multimodal.py --mode features
```
演示特征提取和注意力权重分析。

### 5. 完整训练
```bash
python train_multimodal.py
```
运行完整的交叉验证训练。

## 模型特性

### 技术亮点

1. **多模态融合**
   - 特征级融合而非决策级融合
   - 注意力机制自动学习模态重要性
   - 支持单模态消融研究

2. **图神经网络**
   - 专门处理fMRI功能连接图
   - 多种池化策略提取图级特征
   - 支持不同图拓扑结构

3. **3D卷积网络**
   - 处理sMRI体积数据
   - 保持空间信息完整性
   - 自适应特征维度调整

4. **注意力机制**
   - 双线性注意力融合
   - 自注意力和交叉注意力
   - 可解释的注意力权重

### 训练特性

1. **交叉验证**
   - 分层K折交叉验证
   - 确保类别平衡
   - 被试级别的数据分割

2. **数据增强**
   - sMRI数据的空间变换
   - 强度归一化
   - 随机旋转和翻转

3. **正则化**
   - Dropout防止过拟合
   - 权重衰减
   - 早停机制

4. **优化策略**
   - Adam优化器
   - 学习率自适应调整
   - 梯度裁剪

## 性能评估

### 评估指标
- 准确率 (Accuracy)
- F1分数 (F1-score)
- 混淆矩阵 (Confusion Matrix)
- 分类报告 (Classification Report)

### 输出文件
- `multimodal_cv_results.json`: 交叉验证详细结果
- `multimodal_subject_predictions.csv`: 被试级别预测结果
- `multimodal_confusion_matrix.png`: 混淆矩阵可视化
- `multimodal_classification_report.json`: 分类性能报告

## 系统要求

### 依赖库
```
torch >= 1.9.0
torch-geometric >= 2.0.0
monai >= 0.8.0
nibabel >= 3.2.0
scikit-learn >= 1.0.0
matplotlib >= 3.3.0
seaborn >= 0.11.0
pandas >= 1.3.0
numpy >= 1.21.0
tqdm >= 4.62.0
```

### 硬件要求
- **内存**: 建议16GB以上
- **GPU**: 可选，支持CUDA加速
- **存储**: 根据数据集大小而定

## 扩展功能

### 集成学习
```python
from multimodal_model import create_multimodal_ensemble

# 创建集成模型
ensemble_model = create_multimodal_ensemble(
    num_node_features=4,
    num_models=3,
    num_classes=2
)
```

### 单模态分析
```python
# 仅使用fMRI数据
logits = model(fmri_data, smri_data, use_single_modality='gcn')

# 仅使用sMRI数据
logits = model(fmri_data, smri_data, use_single_modality='vgg')
```

### 特征可视化
```python
# 提取中间特征
features_dict = model.extract_features(fmri_data, smri_data)

# 获取注意力权重
attention_weights = model.get_attention_weights(fmri_data, smri_data)
```

## 故障排除

### 常见问题

1. **数据加载错误**
   - 检查文件路径和命名约定
   - 确保数据格式正确
   - 验证被试ID匹配

2. **内存不足**
   - 减小batch_size
   - 使用数据并行
   - 启用梯度累积

3. **训练不收敛**
   - 调整学习率
   - 检查数据预处理
   - 增加正则化

## 引用

如果您使用了本系统，请引用相关论文和代码库。

## 许可证

本项目遵循MIT许可证。

## 联系方式

如有问题或建议，请联系项目维护者。
