import os
import sys
import torch
import numpy as np
from torch.utils.data import DataLoader
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from multimodal_dataset import MultiModalDataset, create_multimodal_transforms, multimodal_collate_fn
from multimodal_model import create_multimodal_classifier
from gcn_feature_extractor import create_gcn_feature_extractor
from vgg3d_feature_extractor import create_vgg3d_feature_extractor
from bilinear_attention_fusion import BilinearAttentionFusion

def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    try:
        # 创建数据变换
        train_transform, val_transform = create_multimodal_transforms()
        
        # 创建数据集
        dataset = MultiModalDataset(
            fmri_dir='./data_fmri',
            smri_dir='./data_smri',
            edge_lists_dir='./edge_lists',
            node_features_dir='./node_features',
            classes=['HC', 'MCI'],
            transform_smri=val_transform,
            use_edge_lists=True
        )
        
        print(f"✓ 数据集创建成功，包含 {len(dataset)} 个样本")
        
        # 测试单个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"✓ 样本结构:")
            print(f"  - fMRI数据: {type(sample['fmri'])}")
            print(f"    - 节点特征形状: {sample['fmri'].x.shape}")
            print(f"    - 边索引形状: {sample['fmri'].edge_index.shape}")
            print(f"  - sMRI数据形状: {sample['smri'].shape}")
            print(f"  - 标签: {sample['label']}")
            print(f"  - 被试ID: {sample['subject_id']}")
            
            # 测试数据加载器
            dataloader = DataLoader(
                dataset, 
                batch_size=2, 
                shuffle=False,
                collate_fn=multimodal_collate_fn
            )
            
            batch = next(iter(dataloader))
            print(f"✓ 批处理数据:")
            print(f"  - fMRI批次: {type(batch['fmri'])}")
            print(f"    - 批次节点特征形状: {batch['fmri'].x.shape}")
            print(f"    - 批次边索引形状: {batch['fmri'].edge_index.shape}")
            print(f"    - 批次索引形状: {batch['fmri'].batch.shape}")
            print(f"  - sMRI批次形状: {batch['smri'].shape}")
            print(f"  - 标签批次形状: {batch['labels'].shape}")
            
            return sample['fmri'].x.shape[1]  # 返回节点特征维度
        else:
            print("✗ 数据集为空")
            return None
            
    except Exception as e:
        print(f"✗ 数据加载测试失败: {str(e)}")
        return None

def test_individual_models(num_node_features):
    """测试单独的模型组件"""
    print("\n" + "=" * 50)
    print("测试单独的模型组件")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 测试GCN特征提取器
        print("测试GCN特征提取器...")
        gcn_extractor = create_gcn_feature_extractor(
            num_node_features=num_node_features,
            hidden_channels=64,
            feature_dim=128
        ).to(device)
        
        # 创建测试数据
        x = torch.randn(10, num_node_features).to(device)
        edge_index = torch.tensor([[0, 1, 2, 3], [1, 2, 3, 0]], dtype=torch.long).to(device)
        batch = torch.zeros(10, dtype=torch.long).to(device)
        
        gcn_features = gcn_extractor(x, edge_index, batch, return_features=True)
        print(f"✓ GCN特征提取器输出形状: {gcn_features.shape}")
        
        # 测试3D VGG特征提取器
        print("测试3D VGG特征提取器...")
        vgg_extractor = create_vgg3d_feature_extractor(
            model_name="vgg16",
            in_channels=1,
            feature_dim=128
        ).to(device)
        
        # 创建测试数据
        smri_data = torch.randn(1, 1, 64, 128, 128).to(device)
        vgg_features = vgg_extractor(smri_data, return_features=True)
        print(f"✓ VGG特征提取器输出形状: {vgg_features.shape}")
        
        # 测试双线性注意力融合
        print("测试双线性注意力融合...")
        fusion_module = BilinearAttentionFusion(
            feature_dim1=128,
            feature_dim2=128,
            fusion_dim=256
        ).to(device)
        
        fused_features, attention_weights = fusion_module(gcn_features, vgg_features)
        print(f"✓ 融合特征形状: {fused_features.shape}")
        print(f"✓ 注意力权重形状: {attention_weights.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 单独模型测试失败: {str(e)}")
        return False

def test_multimodal_model(num_node_features):
    """测试完整的多模态模型"""
    print("\n" + "=" * 50)
    print("测试完整的多模态模型")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 创建多模态模型
        model = create_multimodal_classifier(
            num_node_features=num_node_features,
            gcn_hidden_channels=64,
            gcn_feature_dim=128,
            vgg_feature_dim=128,
            fusion_dim=256,
            num_classes=2
        ).to(device)
        
        print(f"✓ 多模态模型创建成功")
        
        # 创建测试数据
        from torch_geometric.data import Data, Batch
        
        # fMRI图数据
        x = torch.randn(20, num_node_features)
        edge_index = torch.tensor([[0, 1, 2, 3, 10, 11, 12, 13], 
                                  [1, 2, 3, 0, 11, 12, 13, 10]], dtype=torch.long)
        
        # 创建两个图的批次
        data1 = Data(x=x[:10], edge_index=edge_index[:, :4])
        data2 = Data(x=x[10:], edge_index=edge_index[:, 4:] - 10)
        fmri_batch = Batch.from_data_list([data1, data2]).to(device)
        
        # sMRI数据
        smri_batch = torch.randn(2, 1, 64, 128, 128).to(device)
        
        # 测试前向传播
        logits = model(fmri_batch, smri_batch)
        print(f"✓ 模型前向传播成功，输出形状: {logits.shape}")
        
        # 测试特征提取
        logits, features = model(fmri_batch, smri_batch, return_features=True)
        print(f"✓ 特征提取成功:")
        print(f"  - GCN特征形状: {features['gcn_features'].shape}")
        print(f"  - VGG特征形状: {features['vgg_features'].shape}")
        print(f"  - 融合特征形状: {features['fused_features'].shape}")
        print(f"  - 注意力权重形状: {features['attention_weights'].shape}")
        
        # 测试单模态分类
        gcn_logits = model(fmri_batch, smri_batch, use_single_modality='gcn')
        vgg_logits = model(fmri_batch, smri_batch, use_single_modality='vgg')
        print(f"✓ 单模态分类测试成功:")
        print(f"  - GCN单模态输出形状: {gcn_logits.shape}")
        print(f"  - VGG单模态输出形状: {vgg_logits.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 多模态模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_training_pipeline():
    """测试训练流程"""
    print("\n" + "=" * 50)
    print("测试训练流程")
    print("=" * 50)
    
    try:
        # 创建数据变换
        train_transform, val_transform = create_multimodal_transforms()
        
        # 创建数据集
        dataset = MultiModalDataset(
            fmri_dir='./data_fmri',
            smri_dir='./data_smri',
            edge_lists_dir='./edge_lists',
            node_features_dir='./node_features',
            classes=['HC', 'MCI'],
            transform_smri=None,  # 在训练循环中应用
            use_edge_lists=True
        )
        
        if len(dataset) < 2:
            print("✗ 数据集样本不足，无法测试训练流程")
            return False
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset, 
            batch_size=2, 
            shuffle=True,
            collate_fn=multimodal_collate_fn
        )
        
        # 获取节点特征维度
        sample = dataset[0]
        num_node_features = sample['fmri'].x.shape[1]
        
        # 创建模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = create_multimodal_classifier(
            num_node_features=num_node_features,
            num_classes=2
        ).to(device)
        
        # 创建优化器和损失函数
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = torch.nn.CrossEntropyLoss()
        
        # 测试一个训练步骤
        model.train()
        batch = next(iter(dataloader))

        # 应用变换到每个样本
        smri_batch = []
        for i in range(batch['smri'].shape[0]):
            smri_sample = batch['smri'][i]  # [D, H, W]
            smri_transformed = train_transform(smri_sample)  # 应用变换
            smri_batch.append(smri_transformed)
        batch['smri'] = torch.stack(smri_batch)  # [batch_size, 1, D, H, W]

        # 移动到设备
        fmri_data = batch['fmri'].to(device)
        smri_data = batch['smri'].to(device)
        labels = batch['labels'].to(device)
        
        # 前向传播
        optimizer.zero_grad()
        logits = model(fmri_data, smri_data)
        loss = criterion(logits, labels)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        print(f"✓ 训练步骤测试成功:")
        print(f"  - 损失值: {loss.item():.4f}")
        print(f"  - 输出形状: {logits.shape}")
        
        # 测试评估模式
        model.eval()
        with torch.no_grad():
            eval_logits = model(fmri_data, smri_data)
            eval_loss = criterion(eval_logits, labels)
        
        print(f"✓ 评估步骤测试成功:")
        print(f"  - 评估损失: {eval_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("多模态系统测试开始...")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA设备: {torch.cuda.get_device_name()}")
    
    # 测试数据加载
    num_node_features = test_data_loading()
    if num_node_features is None:
        print("\n✗ 数据加载测试失败，无法继续后续测试")
        return
    
    # 测试单独模型组件
    if not test_individual_models(num_node_features):
        print("\n✗ 单独模型测试失败")
        return
    
    # 测试完整多模态模型
    if not test_multimodal_model(num_node_features):
        print("\n✗ 多模态模型测试失败")
        return
    
    # 测试训练流程
    if not test_training_pipeline():
        print("\n✗ 训练流程测试失败")
        return
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！多模态系统准备就绪")
    print("=" * 50)
    print("\n可以运行以下命令开始训练:")
    print("python train_multimodal.py")

if __name__ == "__main__":
    main()
