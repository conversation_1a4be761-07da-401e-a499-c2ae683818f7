#!/usr/bin/env python3
"""
多模态深度学习模型演示脚本
结合fMRI功能连接矩阵和sMRI灰质体积图进行HC/MCI分类

使用方法:
python demo_multimodal.py --mode [train|test|demo]
"""

import argparse
import os
import torch
import numpy as np
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import seaborn as sns

from multimodal_dataset import MultiModalDataset, create_multimodal_transforms, multimodal_collate_fn
from multimodal_model import create_multimodal_classifier
from train_multimodal import MultiModalTrainer

def demo_data_exploration():
    """演示数据探索功能"""
    print("=" * 60)
    print("多模态数据探索演示")
    print("=" * 60)
    
    # 创建数据集
    train_transform, val_transform = create_multimodal_transforms()
    dataset = MultiModalDataset(
        fmri_dir='./data_fmri',
        smri_dir='./data_smri',
        edge_lists_dir='./edge_lists',
        node_features_dir='./node_features',
        classes=['HC', 'MCI'],
        transform_smri=None,
        use_edge_lists=True
    )
    
    print(f"数据集总样本数: {len(dataset)}")
    
    # 统计各类别样本数
    class_counts = {'HC': 0, 'MCI': 0}
    for i in range(len(dataset)):
        sample = dataset[i]
        label = sample['label'].item()
        if label == 0:
            class_counts['HC'] += 1
        else:
            class_counts['MCI'] += 1
    
    print(f"HC样本数: {class_counts['HC']}")
    print(f"MCI样本数: {class_counts['MCI']}")
    
    # 分析数据维度
    sample = dataset[0]
    print(f"\n数据维度分析:")
    print(f"fMRI图数据:")
    print(f"  - 节点数: {sample['fmri'].x.shape[0]}")
    print(f"  - 节点特征维度: {sample['fmri'].x.shape[1]}")
    print(f"  - 边数: {sample['fmri'].edge_index.shape[1]}")
    print(f"sMRI体积数据:")
    print(f"  - 体积形状: {sample['smri'].shape}")
    
    # 可视化类别分布
    plt.figure(figsize=(8, 6))
    classes = list(class_counts.keys())
    counts = list(class_counts.values())
    colors = ['skyblue', 'lightcoral']
    
    plt.bar(classes, counts, color=colors)
    plt.title('数据集类别分布')
    plt.xlabel('类别')
    plt.ylabel('样本数')
    plt.grid(axis='y', alpha=0.3)
    
    for i, count in enumerate(counts):
        plt.text(i, count + 0.5, str(count), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('dataset_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n类别分布图已保存为: dataset_distribution.png")

def demo_model_architecture():
    """演示模型架构"""
    print("\n" + "=" * 60)
    print("多模态模型架构演示")
    print("=" * 60)
    
    # 创建模型
    model = create_multimodal_classifier(
        num_node_features=4,  # 基于实际数据
        gcn_hidden_channels=64,
        gcn_feature_dim=128,
        vgg_feature_dim=128,
        fusion_dim=256,
        num_classes=2
    )
    
    # 打印模型结构
    print("模型架构:")
    print(model)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n模型参数统计:")
    print(f"总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")
    
    # 分析各组件参数
    gcn_params = sum(p.numel() for p in model.gcn_extractor.parameters())
    vgg_params = sum(p.numel() for p in model.vgg_extractor.parameters())
    fusion_params = sum(p.numel() for p in model.fusion_module.parameters())
    classifier_params = sum(p.numel() for p in model.final_classifier.parameters())
    
    print(f"\n各组件参数分布:")
    print(f"GCN特征提取器: {gcn_params:,} ({gcn_params/total_params*100:.1f}%)")
    print(f"VGG特征提取器: {vgg_params:,} ({vgg_params/total_params*100:.1f}%)")
    print(f"融合模块: {fusion_params:,} ({fusion_params/total_params*100:.1f}%)")
    print(f"分类器: {classifier_params:,} ({classifier_params/total_params*100:.1f}%)")

def demo_feature_extraction():
    """演示特征提取功能"""
    print("\n" + "=" * 60)
    print("特征提取演示")
    print("=" * 60)
    
    # 加载数据
    dataset = MultiModalDataset(
        fmri_dir='./data_fmri',
        smri_dir='./data_smri',
        edge_lists_dir='./edge_lists',
        node_features_dir='./node_features',
        classes=['HC', 'MCI'],
        transform_smri=None,
        use_edge_lists=True
    )
    
    if len(dataset) == 0:
        print("数据集为空，无法演示特征提取")
        return
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset, 
        batch_size=4, 
        shuffle=False,
        collate_fn=multimodal_collate_fn
    )
    
    # 创建模型
    sample = dataset[0]
    num_node_features = sample['fmri'].x.shape[1]
    
    model = create_multimodal_classifier(
        num_node_features=num_node_features,
        num_classes=2
    )
    model.eval()
    
    # 提取特征
    train_transform, val_transform = create_multimodal_transforms()
    
    with torch.no_grad():
        batch = next(iter(dataloader))
        
        # 应用变换
        smri_batch = []
        for i in range(batch['smri'].shape[0]):
            smri_sample = batch['smri'][i]
            smri_transformed = val_transform(smri_sample)
            smri_batch.append(smri_transformed)
        batch['smri'] = torch.stack(smri_batch)
        
        # 提取特征
        features_dict = model.extract_features(batch['fmri'], batch['smri'])
        attention_weights = model.get_attention_weights(batch['fmri'], batch['smri'])
        
        print(f"特征提取结果:")
        print(f"GCN特征形状: {features_dict['gcn_features'].shape}")
        print(f"VGG特征形状: {features_dict['vgg_features'].shape}")
        print(f"融合特征形状: {features_dict['fused_features'].shape}")
        print(f"注意力权重形状: {attention_weights.shape}")
        
        # 分析注意力权重
        print(f"\n注意力权重分析:")
        avg_attention = attention_weights.mean(dim=0)
        print(f"平均注意力权重 - GCN: {avg_attention[0]:.4f}, VGG: {avg_attention[1]:.4f}")
        
        # 可视化注意力权重
        plt.figure(figsize=(10, 6))
        
        plt.subplot(1, 2, 1)
        plt.bar(['GCN (fMRI)', 'VGG (sMRI)'], avg_attention.numpy())
        plt.title('平均注意力权重')
        plt.ylabel('注意力权重')
        plt.grid(axis='y', alpha=0.3)
        
        plt.subplot(1, 2, 2)
        attention_np = attention_weights.numpy()
        plt.boxplot([attention_np[:, 0], attention_np[:, 1]], 
                   labels=['GCN (fMRI)', 'VGG (sMRI)'])
        plt.title('注意力权重分布')
        plt.ylabel('注意力权重')
        plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('attention_weights_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"注意力权重分析图已保存为: attention_weights_analysis.png")

def demo_training():
    """演示训练过程"""
    print("\n" + "=" * 60)
    print("训练演示")
    print("=" * 60)
    
    # 简化的训练配置（用于演示）
    config = {
        'fmri_dir': './data_fmri',
        'smri_dir': './data_smri',
        'edge_lists_dir': './edge_lists',
        'node_features_dir': './node_features',
        'classes': ['HC', 'MCI'],
        'use_edge_lists': True,
        'gcn_hidden_channels': 32,  # 减小模型以加快演示
        'gcn_feature_dim': 64,
        'vgg_feature_dim': 64,
        'fusion_dim': 128,
        'num_classes': 2,
        'batch_size': 4,
        'learning_rate': 0.001,
        'weight_decay': 1e-4,
        'epochs': 5,  # 仅演示几个epoch
        'patience': 3,
        'n_splits': 2,  # 2折交叉验证
    }
    
    print("开始演示训练...")
    print("注意: 这是一个简化的演示，仅运行少量epoch")
    
    trainer = MultiModalTrainer(config)
    fold_results = trainer.cross_validation()
    
    print(f"\n演示训练完成!")
    print(f"结果已保存到相应文件中")

def main():
    parser = argparse.ArgumentParser(description='多模态深度学习模型演示')
    parser.add_argument('--mode', choices=['explore', 'architecture', 'features', 'train', 'all'], 
                       default='all', help='演示模式')
    
    args = parser.parse_args()
    
    print("多模态深度学习系统演示")
    print("结合fMRI功能连接和sMRI灰质体积进行HC/MCI分类")
    print("=" * 60)
    
    if args.mode in ['explore', 'all']:
        demo_data_exploration()
    
    if args.mode in ['architecture', 'all']:
        demo_model_architecture()
    
    if args.mode in ['features', 'all']:
        demo_feature_extraction()
    
    if args.mode in ['train', 'all']:
        print("\n如需运行完整训练，请使用:")
        print("python train_multimodal.py")
        print("\n如需运行演示训练，请使用:")
        print("python demo_multimodal.py --mode train")
    
    print("\n" + "=" * 60)
    print("演示完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
